// AI-Assisted Email Response System - Ingestion Service HTTP Server
// Provides REST API endpoints for the desktop application

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    <PERSON><PERSON>,
    <PERSON><PERSON> as JsonExtractor,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::cors::CorsLayer;
use tower_http::trace::TraceLayer;
use tracing::{info, warn, error};
use uuid::Uuid;

use ingestion_service::*;

/// Safely truncates a string at a UTF-8 character boundary
/// Returns the truncated string with "..." appended if truncation occurred
fn safe_truncate(s: &str, max_bytes: usize) -> String {
    if s.len() <= max_bytes {
        return s.to_string();
    }

    // Find the last valid UTF-8 character boundary at or before max_bytes
    let mut boundary = max_bytes;
    while boundary > 0 && !s.is_char_boundary(boundary) {
        boundary -= 1;
    }

    if boundary == 0 {
        // If we can't find any valid boundary, return empty string with ellipsis
        return "...".to_string();
    }

    format!("{}...", &s[..boundary])
}

// Application state
#[derive(Clone)]
pub struct AppState {
    pub qdrant_client: Arc<qdrant_client::Qdrant>,
    pub embedding_service_url: String,
}

// API Request/Response types
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub message: String,
    pub services: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailProcessingRequest {
    pub file_path: String,
    pub file_type: String, // "eml" or "mbox"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailProcessingResponse {
    pub success: bool,
    pub message: String,
    pub processed_count: Option<u32>,
    pub processed_ids: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailListResponse {
    pub emails: Vec<EmailSummary>,
    pub total_count: u64,
    pub page: u64,
    pub page_size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailSummary {
    pub id: Uuid,
    pub subject: String,
    pub from_address: String,
    pub sent_date: String,
    pub preview: String, // First 200 characters of content
    pub thread_id: Option<String>,
    pub conversation_id: Option<String>,
    pub email_type: Option<String>,
    pub is_duplicate: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailDetailResponse {
    pub email: Message,
}

#[derive(Debug, Deserialize)]
pub struct ListEmailsQuery {
    pub page: Option<u64>,
    pub page_size: Option<u64>,
}

#[derive(Debug, Deserialize)]
pub struct SearchEmailsQuery {
    pub query: String,
    pub limit: Option<u64>,
    pub score_threshold: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchEmailsResponse {
    pub results: Vec<SearchResultSummary>,
    pub query: String,
    pub total_found: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResultSummary {
    pub email: EmailSummary,
    pub score: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreadEmail {
    pub id: Uuid,
    pub subject: String,
    pub from_address: String,
    pub to_addresses: Vec<String>,
    pub sent_date: String,
    pub cleaned_plain_text_body: String,
    pub thread_position: Option<u32>,
    pub email_type: String,
    pub email_weight: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ConversationThreadResponse {
    pub thread_id: String,
    pub emails: Vec<ThreadEmail>,
    pub total_count: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CaseEmailsResponse {
    pub case_id: String,
    pub emails: Vec<EmailSummary>,
    pub total_count: u32,
}

// Health check endpoint
async fn health_check(State(state): State<AppState>) -> Result<Json<HealthResponse>, StatusCode> {
    let mut services = HashMap::new();
    
    // Check Qdrant connection
    match state.qdrant_client.health_check().await {
        Ok(_) => {
            services.insert("qdrant".to_string(), "healthy".to_string());
        }
        Err(_) => {
            services.insert("qdrant".to_string(), "unhealthy".to_string());
        }
    }
    
    // Check embedding service
    let client = reqwest::Client::new();
    match client.get(&format!("{}/health", state.embedding_service_url)).send().await {
        Ok(response) if response.status().is_success() => {
            services.insert("embedding_service".to_string(), "healthy".to_string());
        }
        _ => {
            services.insert("embedding_service".to_string(), "unhealthy".to_string());
        }
    }
    
    let all_healthy = services.values().all(|status| status == "healthy");
    
    Ok(Json(HealthResponse {
        status: if all_healthy { "healthy".to_string() } else { "degraded".to_string() },
        message: "Ingestion service is running".to_string(),
        services,
    }))
}

// Process email files endpoint
#[axum::debug_handler]
async fn process_emails(
    State(state): State<AppState>,
    JsonExtractor(request): JsonExtractor<EmailProcessingRequest>,
) -> Result<Json<EmailProcessingResponse>, StatusCode> {
    info!("Processing email file: {} (type: {})", request.file_path, request.file_type);
    
    match request.file_type.as_str() {
        "eml" => {
            match ingest_email_file_with_embeddings(
                &*state.qdrant_client,
                &request.file_path,
                &state.embedding_service_url,
            ).await {
                Ok(message_id) => {
                    info!("Successfully processed EML file: {}", message_id);
                    Ok(Json(EmailProcessingResponse {
                        success: true,
                        message: "Email processed successfully".to_string(),
                        processed_count: Some(1),
                        processed_ids: Some(vec![message_id.to_string()]),
                    }))
                }
                Err(e) => {
                    error!("Failed to process EML file: {}", e);
                    Ok(Json(EmailProcessingResponse {
                        success: false,
                        message: format!("Failed to process email: {}", e),
                        processed_count: Some(0),
                        processed_ids: None,
                    }))
                }
            }
        }
        "mbox" | "thunderbird" => {
            match ingest_mbox_file_with_embeddings(
                &*state.qdrant_client,
                &request.file_path,
                &state.embedding_service_url,
            ).await {
                Ok(message_ids) => {
                    info!("Successfully processed MBOX file with {} emails", message_ids.len());
                    Ok(Json(EmailProcessingResponse {
                        success: true,
                        message: format!("Processed {} emails from MBOX file", message_ids.len()),
                        processed_count: Some(message_ids.len() as u32),
                        processed_ids: Some(message_ids.into_iter().map(|id| id.to_string()).collect()),
                    }))
                }
                Err(e) => {
                    error!("Failed to process MBOX file: {}", e);
                    Ok(Json(EmailProcessingResponse {
                        success: false,
                        message: format!("Failed to process MBOX file: {}", e),
                        processed_count: Some(0),
                        processed_ids: None,
                    }))
                }
            }
        }
        "auto" => {
            // Auto-detect file format
            if request.file_path.ends_with(".eml") {
                // Process as EML
                match ingest_email_file_with_embeddings(
                    &*state.qdrant_client,
                    &request.file_path,
                    &state.embedding_service_url,
                ).await {
                    Ok(message_id) => {
                        info!("Successfully auto-detected and processed EML file: {}", message_id);
                        Ok(Json(EmailProcessingResponse {
                            success: true,
                            message: "Email processed successfully (auto-detected as EML)".to_string(),
                            processed_count: Some(1),
                            processed_ids: Some(vec![message_id.to_string()]),
                        }))
                    }
                    Err(e) => {
                        error!("Failed to process auto-detected EML file: {}", e);
                        Ok(Json(EmailProcessingResponse {
                            success: false,
                            message: format!("Failed to process email: {}", e),
                            processed_count: Some(0),
                            processed_ids: None,
                        }))
                    }
                }
            } else if request.file_path.ends_with(".mbox") || is_thunderbird_mbox(&request.file_path) {
                // Process as MBOX/Thunderbird
                match ingest_mbox_file_with_embeddings(
                    &*state.qdrant_client,
                    &request.file_path,
                    &state.embedding_service_url,
                ).await {
                    Ok(message_ids) => {
                        info!("Successfully auto-detected and processed MBOX/Thunderbird file with {} emails", message_ids.len());
                        Ok(Json(EmailProcessingResponse {
                            success: true,
                            message: format!("Processed {} emails successfully (auto-detected as MBOX/Thunderbird)", message_ids.len()),
                            processed_count: Some(message_ids.len() as u32),
                            processed_ids: Some(message_ids.iter().map(|id| id.to_string()).collect()),
                        }))
                    }
                    Err(e) => {
                        error!("Failed to process auto-detected MBOX/Thunderbird file: {}", e);
                        Ok(Json(EmailProcessingResponse {
                            success: false,
                            message: format!("Failed to process MBOX file: {}", e),
                            processed_count: Some(0),
                            processed_ids: None,
                        }))
                    }
                }
            } else {
                warn!("Could not auto-detect file type for: {}", request.file_path);
                Ok(Json(EmailProcessingResponse {
                    success: false,
                    message: "Could not auto-detect file format. Please specify 'eml', 'mbox', or 'thunderbird'".to_string(),
                    processed_count: Some(0),
                    processed_ids: None,
                }))
            }
        }
        _ => {
            warn!("Unsupported file type: {}", request.file_type);
            Ok(Json(EmailProcessingResponse {
                success: false,
                message: "Unsupported file type. Use 'eml', 'mbox', 'thunderbird', or 'auto'".to_string(),
                processed_count: Some(0),
                processed_ids: None,
            }))
        }
    }
}

// List processed emails endpoint
async fn list_emails(
    State(state): State<AppState>,
    Query(params): Query<ListEmailsQuery>,
) -> Result<Json<EmailListResponse>, StatusCode> {
    let page = params.page.unwrap_or(1);
    let page_size = params.page_size.unwrap_or(20).min(100); // Cap at 100 items per page

    info!("Listing emails: page {}, page_size {}", page, page_size);

    // First get total count by fetching all messages
    match get_recent_messages(&*state.qdrant_client, None).await {
        Ok(all_messages) => {
            let total_count = all_messages.len() as u64;

            // Sort by sent_date (newest first) then by created_at as fallback
            let mut sorted_messages = all_messages;
            sorted_messages.sort_by(|a, b| {
                let date_a = a.sent_date.unwrap_or(a.created_at);
                let date_b = b.sent_date.unwrap_or(b.created_at);
                date_b.cmp(&date_a) // Newest first
            });

            // Apply pagination
            let offset = ((page - 1) * page_size) as usize;
            let end = std::cmp::min(offset + page_size as usize, sorted_messages.len());
            let page_messages = if offset < sorted_messages.len() {
                sorted_messages[offset..end].to_vec()
            } else {
                Vec::new()
            };

            let email_summaries: Vec<EmailSummary> = page_messages
                .into_iter()
                .map(|msg| EmailSummary {
                    id: msg.id,
                    subject: msg.subject.unwrap_or_default(),
                    from_address: msg.from_address.unwrap_or_default(),
                    sent_date: msg.sent_date.map(|dt| dt.to_rfc3339()).unwrap_or_default(),
                    preview: msg.cleaned_plain_text_body
                        .map(|content| safe_truncate(&content, 200))
                        .unwrap_or_default(),
                    thread_id: msg.thread_id,
                    conversation_id: msg.conversation_id,
                    email_type: msg.email_type,
                    is_duplicate: msg.is_duplicate,
                })
                .collect();

            Ok(Json(EmailListResponse {
                total_count,
                emails: email_summaries,
                page,
                page_size,
            }))
        }
        Err(e) => {
            error!("Failed to retrieve emails: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

// Get email details endpoint
async fn get_email_details(
    State(state): State<AppState>,
    Path(email_id): Path<String>,
) -> Result<Json<EmailDetailResponse>, StatusCode> {
    info!("Getting email details for ID: {}", email_id);
    
    // Parse UUID
    let uuid = match Uuid::parse_str(&email_id) {
        Ok(uuid) => uuid,
        Err(_) => {
            warn!("Invalid email ID format: {}", email_id);
            return Err(StatusCode::BAD_REQUEST);
        }
    };
    
    // Search through ALL messages to find the one with matching ID
    // In a production system, you'd want a more efficient lookup
    match get_recent_messages(&*state.qdrant_client, None).await {
        Ok(messages) => {
            if let Some(message) = messages.into_iter().find(|msg| msg.id == uuid) {
                Ok(Json(EmailDetailResponse { email: message }))
            } else {
                warn!("Email not found: {}", email_id);
                Err(StatusCode::NOT_FOUND)
            }
        }
        Err(e) => {
            error!("Failed to retrieve email details: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

// Search emails endpoint
#[axum::debug_handler]
async fn search_emails(
    State(state): State<AppState>,
    Query(params): Query<SearchEmailsQuery>,
) -> Result<Json<SearchEmailsResponse>, StatusCode> {
    let limit = params.limit.unwrap_or(10).min(50); // Cap at 50 results
    let score_threshold = params.score_threshold.unwrap_or(0.5);

    info!("Searching emails with query: '{}', limit: {}, threshold: {}",
          params.query, limit, score_threshold);

    // Generate embedding for the search query
    match get_embedding(&params.query, &state.embedding_service_url).await {
        Ok(query_embedding) => {
            match search_similar_messages(
                &*state.qdrant_client,
                query_embedding,
                Some(limit),
                Some(score_threshold),
            ).await {
                Ok(search_results) => {
                    let result_summaries: Vec<SearchResultSummary> = search_results
                        .into_iter()
                        .map(|result| SearchResultSummary {
                            email: EmailSummary {
                                id: result.message.id,
                                subject: result.message.subject.unwrap_or_default(),
                                from_address: result.message.from_address.unwrap_or_default(),
                                sent_date: result.message.sent_date.map(|dt| dt.to_rfc3339()).unwrap_or_default(),
                                preview: result.message.cleaned_plain_text_body
                                    .map(|content| safe_truncate(&content, 200))
                                    .unwrap_or_default(),
                                thread_id: result.message.thread_id,
                                conversation_id: result.message.conversation_id,
                                email_type: result.message.email_type,
                                is_duplicate: result.message.is_duplicate,
                            },
                            score: result.score,
                        })
                        .collect();

                    Ok(Json(SearchEmailsResponse {
                        results: result_summaries.clone(),
                        query: params.query,
                        total_found: result_summaries.len(),
                    }))
                }
                Err(e) => {
                    error!("Failed to search emails: {}", e);
                    Err(StatusCode::INTERNAL_SERVER_ERROR)
                }
            }
        }
        Err(e) => {
            error!("Failed to generate embedding for search query: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Search emails with thread-aware prioritization
async fn search_emails_with_thread_priority(
    State(state): State<AppState>,
    Query(params): Query<HashMap<String, String>>,
) -> Result<Json<Vec<EmailSummary>>, StatusCode> {
    let query = params.get("q").ok_or(StatusCode::BAD_REQUEST)?;
    let limit = params.get("limit")
        .and_then(|l| l.parse::<u64>().ok())
        .unwrap_or(10);
    let thread_id = params.get("thread_id").cloned();
    let conversation_id = params.get("conversation_id").cloned();

    info!("Thread-aware search request: query='{}', limit={}, thread_id={:?}, conversation_id={:?}",
          query, limit, thread_id, conversation_id);

    // Generate embedding for the query
    let embedding = match get_embedding(query, &state.embedding_service_url).await {
        Ok(emb) => emb,
        Err(e) => {
            error!("Failed to generate embedding: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // Search with thread priority
    let messages = match ingestion_service::search_similar_messages_with_thread_priority(
        &state.qdrant_client,
        embedding,
        limit,
        thread_id,
        conversation_id,
        "emails"
    ).await {
        Ok(msgs) => msgs,
        Err(e) => {
            error!("Thread-aware search failed: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    let summaries: Vec<EmailSummary> = messages.into_iter().map(|msg| {
        EmailSummary {
            id: msg.id,
            subject: msg.subject.unwrap_or_default(),
            from_address: msg.from_address.unwrap_or_default(),
            sent_date: msg.sent_date.map(|d| d.to_rfc3339()).unwrap_or_default(),
            preview: msg.cleaned_plain_text_body
                .map(|body| safe_truncate(&body, 200))
                .unwrap_or_default(),
            thread_id: msg.thread_id,
            conversation_id: msg.conversation_id,
            email_type: msg.email_type,
            is_duplicate: msg.is_duplicate,
        }
    }).collect();

    info!("Thread-aware search returned {} results", summaries.len());
    Ok(Json(summaries))
}

/// Get complete conversation thread
async fn get_conversation_thread(
    State(state): State<AppState>,
    Path(thread_id): Path<String>,
) -> Result<Json<ConversationThreadResponse>, StatusCode> {
    info!("Getting conversation thread: {}", thread_id);

    let messages = match ingestion_service::get_conversation_thread(
        &state.qdrant_client,
        thread_id.clone(),
        "emails"
    ).await {
        Ok(msgs) => msgs,
        Err(e) => {
            error!("Failed to get conversation thread: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    let thread_emails: Vec<ThreadEmail> = messages.into_iter().map(|msg| {
        ThreadEmail {
            id: msg.id,
            subject: msg.subject.unwrap_or_default(),
            from_address: msg.from_address.unwrap_or_default(),
            to_addresses: msg.to_addresses,
            sent_date: msg.sent_date.map(|d| d.to_rfc3339()).unwrap_or_default(),
            cleaned_plain_text_body: msg.cleaned_plain_text_body.unwrap_or_default(),
            thread_position: msg.thread_position,
            email_type: msg.email_type.unwrap_or_default(),
            email_weight: msg.email_weight,
        }
    }).collect();

    let total_count = thread_emails.len() as u32;
    let response = ConversationThreadResponse {
        thread_id,
        emails: thread_emails,
        total_count,
    };

    info!("Conversation thread returned {} emails", response.total_count);
    Ok(Json(response))
}

/// Get emails for a specific case
async fn get_case_emails(
    State(state): State<AppState>,
    Path(case_id): Path<String>,
) -> Result<Json<CaseEmailsResponse>, StatusCode> {
    info!("Getting case emails: {}", case_id);

    let messages = match ingestion_service::search_emails_by_case(
        &state.qdrant_client,
        case_id.clone(),
        "emails"
    ).await {
        Ok(msgs) => msgs,
        Err(e) => {
            error!("Failed to get case emails: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    let case_emails: Vec<EmailSummary> = messages.into_iter().map(|msg| {
        EmailSummary {
            id: msg.id,
            subject: msg.subject.unwrap_or_default(),
            from_address: msg.from_address.unwrap_or_default(),
            sent_date: msg.sent_date.map(|d| d.to_rfc3339()).unwrap_or_default(),
            preview: msg.cleaned_plain_text_body
                .map(|body| safe_truncate(&body, 200))
                .unwrap_or_default(),
            thread_id: msg.thread_id,
            conversation_id: msg.conversation_id,
            email_type: msg.email_type,
            is_duplicate: msg.is_duplicate,
        }
    }).collect();

    let total_count = case_emails.len() as u32;
    let response = CaseEmailsResponse {
        case_id,
        emails: case_emails,
        total_count,
    };

    info!("Case emails returned {} emails", response.total_count);
    Ok(Json(response))
}

// Create the router
fn create_router(state: AppState) -> Router {
    Router::new()
        .route("/health", get(health_check))
        .route("/process", post(process_emails))
        .route("/search", get(search_emails))
        .route("/search/thread", get(search_emails_with_thread_priority))
        .route("/conversations/:thread_id", get(get_conversation_thread))
        .route("/cases/:case_id/emails", get(get_case_emails))
        .route("/emails", get(list_emails))
        .route("/emails/:id", get(get_email_details))
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive())
        )
        .with_state(state)
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    info!("Starting AI Email Assistant - Ingestion Service HTTP Server");

    // Load environment variables
    dotenv::dotenv().ok();

    // Establish Qdrant connection
    let qdrant_client = match establish_connection().await {
        Ok(client) => {
            info!("✓ Qdrant connection established");
            Arc::new(client)
        }
        Err(e) => {
            error!("⚠ Qdrant connection failed: {}", e);
            error!("  Make sure Qdrant is running on http://localhost:6334");
            return Err(e);
        }
    };

    // Setup collections
    if let Err(e) = setup_collections(&qdrant_client).await {
        warn!("Warning: Collection setup failed: {}", e);
    } else {
        info!("✓ Qdrant collections ready");
    }

    // Get embedding service URL
    let embedding_service_url = std::env::var("EMBEDDING_SERVICE_URL")
        .unwrap_or_else(|_| "http://localhost:8003".to_string());

    info!("Using embedding service at: {}", embedding_service_url);

    // Create application state
    let app_state = AppState {
        qdrant_client,
        embedding_service_url,
    };

    // Create the router
    let app = create_router(app_state);

    // Start the server
    let port = std::env::var("PORT")
        .unwrap_or_else(|_| "8080".to_string())
        .parse::<u16>()
        .unwrap_or(8080);

    let addr = format!("0.0.0.0:{}", port);
    info!("🚀 Server starting on http://{}", addr);

    let listener = tokio::net::TcpListener::bind(&addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_safe_truncate_ascii() {
        let text = "This is a simple ASCII text that should be truncated properly.";
        let result = safe_truncate(text, 20);
        assert_eq!(result, "This is a simple ASC...");
    }

    #[test]
    fn test_safe_truncate_greek_characters() {
        let text = "Αγαπητέ κύριε Καρυτινέ, Εύχομαι Χρόνια Πολλά και Ευτυχισμένο το Νέο Έτος!";
        let result = safe_truncate(text, 30);

        // Should not panic and should truncate at a valid character boundary
        assert!(result.ends_with("..."));
        assert!(result.len() <= 33); // 30 + "..." = 33

        // Verify the result is valid UTF-8
        assert!(std::str::from_utf8(result.as_bytes()).is_ok());
    }

    #[test]
    fn test_safe_truncate_short_text() {
        let text = "Short";
        let result = safe_truncate(text, 20);
        assert_eq!(result, "Short");
    }

    #[test]
    fn test_safe_truncate_exact_boundary() {
        let text = "Exactly twenty chars";
        let result = safe_truncate(text, 20);
        assert_eq!(result, "Exactly twenty chars");
    }

    #[test]
    fn test_safe_truncate_multibyte_boundary() {
        // Test with text where truncation would fall in the middle of a multi-byte character
        let text = "Hello ύ world"; // 'ύ' is 2 bytes in UTF-8
        let result = safe_truncate(text, 8); // This would fall in the middle of 'ύ'

        // Should truncate at a valid character boundary (either before or after the Greek char)
        assert!(result == "Hello ..." || result == "Hello ύ...");
        assert!(result.ends_with("..."));

        // Most importantly, it should not panic and should be valid UTF-8
        assert!(std::str::from_utf8(result.as_bytes()).is_ok());
    }
}
