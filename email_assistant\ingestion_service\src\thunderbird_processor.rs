// Thunderbird Email Processing Utility
// Main processor for converting Thunderbird files to enhanced mbox format with threading

use std::fs;
use std::path::{Path, PathBuf};
use std::io;
use chrono::Utc;

use crate::{parse_mbox, ParsedEmail};
use crate::email_threading::{ThreadedEmail, EmailThreader, DuplicateDetector};
use crate::enhanced_mbox::EnhancedMboxWriter;
use mail_parser::{MessageParser, HeaderValue};

/// Main Thunderbird processor
pub struct ThunderbirdProcessor {
    threader: EmailThreader,
    duplicate_detector: DuplicateDetector,
    processed_count: u32,
    duplicate_count: u32,
    error_count: u32,
}

impl ThunderbirdProcessor {
    pub fn new() -> Self {
        Self {
            threader: EmailThreader::new(),
            duplicate_detector: DuplicateDetector::new(0.85), // 85% similarity threshold
            processed_count: 0,
            duplicate_count: 0,
            error_count: 0,
        }
    }

    /// Process a Thunderbird directory structure
    pub fn process_thunderbird_directory(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        println!("Processing Thunderbird directory: {}", thunderbird_dir);
        
        let mut writer = EnhancedMboxWriter::new(output_mbox_path)?;
        let mut all_emails = Vec::new();
        
        // Find all mbox files in the directory structure
        let mbox_files = self.find_thunderbird_files(thunderbird_dir)?;
        println!("Found {} Thunderbird files to process", mbox_files.len());
        
        // Process each file and track folder information
        let mut file_email_mapping = Vec::new(); // Track which emails came from which files

        for (file_path, folder_type) in mbox_files {
            println!("Processing file: {} (type: {:?})", file_path.display(), folder_type);

            match self.process_single_file(&file_path, folder_type.clone()) {
                Ok(emails) => {
                    let start_index = all_emails.len();
                    all_emails.extend(emails);
                    let end_index = all_emails.len();

                    // Track which emails came from this file
                    file_email_mapping.push((start_index, end_index, file_path.clone(), folder_type));
                }
                Err(e) => {
                    eprintln!("Error processing file {}: {}", file_path.display(), e);
                    self.error_count += 1;
                }
            }
        }
        
        println!("Parsed {} emails total", all_emails.len());
        
        // Convert to threaded emails and process
        let mut threaded_emails = self.convert_to_threaded_emails(all_emails)?;

        // Set folder information for each email based on source file
        for (start_idx, end_idx, file_path, folder_type) in file_email_mapping {
            for email in &mut threaded_emails[start_idx..end_idx] {
                email.folder_path = Some(file_path.to_string_lossy().to_string());

                // Update email type based on folder
                let folder_path_str = file_path.to_string_lossy();
                email.email_type = EmailThreader::detect_email_type(&folder_path_str, &email.from, &email.to);
                email.weight = email.email_type.default_weight();
            }
        }

        // Sort emails by date for better threading (process chronologically)
        threaded_emails.sort_by(|a, b| {
            a.sent_date.unwrap_or_else(|| Utc::now())
                .cmp(&b.sent_date.unwrap_or_else(|| Utc::now()))
        });

        println!("Sorted {} emails chronologically for combined processing", threaded_emails.len());

        // Apply threading (now processes Inbox + Sent together)
        self.apply_threading(&mut threaded_emails)?;

        // Apply case cataloging
        self.apply_case_cataloging(&mut threaded_emails)?;

        // Detect duplicates (now across both Inbox and Sent)
        self.detect_duplicates(&mut threaded_emails)?;
        
        // Write to enhanced mbox format
        for email in &threaded_emails {
            if !email.is_duplicate {
                writer.write_email(email)?;
                self.processed_count += 1;
            } else {
                self.duplicate_count += 1;
            }
        }
        
        writer.close()?;
        
        Ok(ProcessingReport {
            total_emails: threaded_emails.len() as u32,
            processed_emails: self.processed_count,
            duplicate_emails: self.duplicate_count,
            error_count: self.error_count,
            threads_created: self.threader.threads.len() as u32,
            cases_created: self.threader.cases.len() as u32,
            output_file: output_mbox_path.to_string(),
        })
    }

    /// Find all Thunderbird mbox files in a directory
    fn find_thunderbird_files(&self, dir_path: &str) -> Result<Vec<(PathBuf, FolderType)>, io::Error> {
        let mut files = Vec::new();
        self.scan_directory(Path::new(dir_path), &mut files)?;
        Ok(files)
    }

    /// Recursively scan directory for Thunderbird files
    fn scan_directory(&self, dir: &Path, files: &mut Vec<(PathBuf, FolderType)>) -> Result<(), io::Error> {
        if !dir.is_dir() {
            return Ok(());
        }

        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_dir() {
                // Recursively scan subdirectories
                self.scan_directory(&path, files)?;
            } else if self.is_thunderbird_mbox_file(&path) {
                let folder_type = self.determine_folder_type(&path);
                files.push((path, folder_type));
            }
        }
        
        Ok(())
    }

    /// Check if a file is a Thunderbird mbox file
    fn is_thunderbird_mbox_file(&self, path: &Path) -> bool {
        // Use existing detection logic from lib.rs
        if let Some(path_str) = path.to_str() {
            crate::is_thunderbird_mbox(path_str)
        } else {
            false
        }
    }

    /// Determine folder type from path
    fn determine_folder_type(&self, path: &Path) -> FolderType {
        let path_str = path.to_string_lossy().to_lowercase();
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("")
            .to_lowercase();
        
        if file_name == "inbox" || path_str.contains("inbox") {
            FolderType::Inbox
        } else if file_name == "sent" || path_str.contains("sent") {
            FolderType::Sent
        } else if file_name == "drafts" || path_str.contains("drafts") {
            FolderType::Drafts
        } else if file_name == "trash" || path_str.contains("trash") {
            FolderType::Trash
        } else {
            FolderType::Other(file_name.to_string())
        }
    }

    /// Process a single mbox file
    fn process_single_file(&self, file_path: &Path, _folder_type: FolderType) -> Result<Vec<ParsedEmail>, Box<dyn std::error::Error>> {
        let path_str = file_path.to_str()
            .ok_or("Invalid file path")?;
        
        let emails = parse_mbox(path_str)?;
        println!("  Parsed {} emails from {}", emails.len(), file_path.display());
        
        Ok(emails)
    }

    /// Convert ParsedEmail to ThreadedEmail with enhanced metadata
    fn convert_to_threaded_emails(&self, parsed_emails: Vec<ParsedEmail>) -> Result<Vec<ThreadedEmail>, Box<dyn std::error::Error>> {
        let mut threaded_emails = Vec::new();

        for parsed in parsed_emails {
            // Extract threading headers using mail-parser with error handling
            let (message_id, in_reply_to, references) = if let Some(raw_body) = &parsed.plain_text_body_raw {
                match self.extract_threading_headers(raw_body) {
                    Ok(headers) => headers,
                    Err(e) => {
                        eprintln!("Warning: Failed to extract headers for email {}: {}", parsed.id, e);
                        (None, Vec::new(), Vec::new())
                    }
                }
            } else {
                (None, Vec::new(), Vec::new())
            };

            let normalized_subject = parsed.subject.as_ref()
                .map(|s| EmailThreader::normalize_subject(s));

            // Determine email type from folder path (will be set later) and content
            let email_type = EmailThreader::detect_email_type(
                "", // Will be updated when we process files
                &parsed.from,
                &parsed.to
            );

            let threaded = ThreadedEmail {
                id: parsed.id,
                subject: parsed.subject,
                from: parsed.from,
                to: parsed.to,
                sent_date: parsed.sent_date,
                plain_text_body_raw: parsed.plain_text_body_raw,
                html_body_raw: parsed.html_body_raw,
                cleaned_plain_text_body: parsed.cleaned_plain_text_body,
                message_id,
                in_reply_to,
                references,
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: email_type.clone(),
                weight: email_type.default_weight(),
                folder_path: None, // Will be set based on source file
                content_hash: None,
                normalized_subject,
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: Vec::new(),

                // Initialize case cataloging fields
                case_id: None,
                case_subject: None,
                case_participants: Vec::new(),
            };

            threaded_emails.push(threaded);
        }

        Ok(threaded_emails)
    }

    /// Extract threading headers from email content
    fn extract_threading_headers(&self, content: &str) -> Result<(Option<String>, Vec<String>, Vec<String>), Box<dyn std::error::Error>> {
        // Try to parse the email content to extract headers
        // If parsing fails, we'll return empty values rather than failing completely
        let message = match MessageParser::default().parse(content.as_bytes()) {
            Some(msg) => msg,
            None => {
                // If parsing fails, try to extract headers manually from the raw content
                return self.extract_headers_manually(content);
            }
        };

        let message_id = message.message_id().map(|id| id.to_string());

        // Extract In-Reply-To headers
        let mut in_reply_to = Vec::new();
        for header_value in message.header_values("In-Reply-To") {
            if let HeaderValue::Text(text) = header_value {
                in_reply_to.push(text.to_string());
            }
        }

        // Extract References headers
        let mut references = Vec::new();
        for header_value in message.header_values("References") {
            if let HeaderValue::Text(text) = header_value {
                // References can contain multiple message IDs separated by spaces
                for reference in text.split_whitespace() {
                    references.push(reference.to_string());
                }
            }
        }

        Ok((message_id, in_reply_to, references))
    }

    /// Fallback method to extract headers manually when mail-parser fails
    fn extract_headers_manually(&self, content: &str) -> Result<(Option<String>, Vec<String>, Vec<String>), Box<dyn std::error::Error>> {
        let mut message_id = None;
        let mut in_reply_to = Vec::new();
        let mut references = Vec::new();

        // Look for headers in the first part of the content
        let lines: Vec<&str> = content.lines().take(100).collect(); // Check first 100 lines

        for line in lines {
            let line = line.trim();

            // Extract Message-ID
            if line.starts_with("Message-ID:") || line.starts_with("Message-Id:") {
                if let Some(id) = line.split(':').nth(1) {
                    message_id = Some(id.trim().to_string());
                }
            }

            // Extract In-Reply-To
            if line.starts_with("In-Reply-To:") {
                if let Some(reply_to) = line.split(':').nth(1) {
                    in_reply_to.push(reply_to.trim().to_string());
                }
            }

            // Extract References
            if line.starts_with("References:") {
                if let Some(refs) = line.split(':').nth(1) {
                    for reference in refs.split_whitespace() {
                        references.push(reference.trim().to_string());
                    }
                }
            }

            // Stop at empty line (end of headers)
            if line.is_empty() {
                break;
            }
        }

        Ok((message_id, in_reply_to, references))
    }

    /// Apply threading to all emails
    fn apply_threading(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Applying email threading across combined Inbox and Sent emails...");

        for email in emails.iter_mut() {
            match self.threader.thread_email(email) {
                Ok(thread_id) => {
                    email.processing_notes.push(format!("Threaded into: {}", thread_id));
                }
                Err(e) => {
                    email.processing_notes.push(format!("Threading error: {}", e));
                }
            }
        }

        println!("Created {} conversation threads", self.threader.threads.len());
        Ok(())
    }

    /// Apply case cataloging to all emails
    fn apply_case_cataloging(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Applying case cataloging...");

        for email in emails.iter_mut() {
            match self.threader.catalog_email(email) {
                Ok(()) => {
                    if let Some(case_id) = &email.case_id {
                        email.processing_notes.push(format!("Cataloged into case: {}", case_id));
                    }
                }
                Err(e) => {
                    email.processing_notes.push(format!("Case cataloging error: {}", e));
                }
            }
        }

        println!("Created {} email cases", self.threader.cases.len());
        Ok(())
    }

    /// Detect and mark duplicate emails
    fn detect_duplicates(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Detecting duplicate emails...");
        
        for email in emails.iter_mut() {
            if self.duplicate_detector.check_duplicate(email) {
                email.processing_notes.push("Marked as duplicate".to_string());
            }
        }
        
        Ok(())
    }
}

/// Folder type classification
#[derive(Debug, Clone, PartialEq)]
pub enum FolderType {
    Inbox,
    Sent,
    Drafts,
    Trash,
    Other(String),
}

/// Processing report
#[derive(Debug)]
pub struct ProcessingReport {
    pub total_emails: u32,
    pub processed_emails: u32,
    pub duplicate_emails: u32,
    pub error_count: u32,
    pub threads_created: u32,
    pub cases_created: u32,
    pub output_file: String,
}

impl ProcessingReport {
    pub fn print_summary(&self) {
        println!("\n=== Enhanced Processing Summary ===");
        println!("Total emails found: {}", self.total_emails);
        println!("Emails processed: {}", self.processed_emails);
        println!("Duplicates removed: {}", self.duplicate_emails);
        println!("Errors encountered: {}", self.error_count);
        println!("Conversation threads: {}", self.threads_created);
        println!("Email cases cataloged: {}", self.cases_created);
        println!("Output file: {}", self.output_file);
        println!("===================================");

        if self.cases_created > 0 {
            println!("\n📁 Case cataloging enabled - emails organized by subject and participants");
            println!("🔗 Combined threading - Inbox and Sent emails threaded together");
            println!("🧹 Enhanced deduplication - duplicates detected across all folders");
        }
    }
}
