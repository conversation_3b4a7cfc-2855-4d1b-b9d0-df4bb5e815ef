{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 2040997289075261528, "path": 10440773748300679285, "deps": [[2828590642173593838, "cfg_if", false, 17428403545301830978]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\encoding_rs-01b80a60248c372f\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}