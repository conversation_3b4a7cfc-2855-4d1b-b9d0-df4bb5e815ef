{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2040997289075261528, "path": 442901360617320951, "deps": [[2828590642173593838, "cfg_if", false, 17428403545301830978], [5491919304041016563, "build_script_build", false, 10284292020453284036], [8995469080876806959, "untrusted", false, 9522255962815586883], [9920160576179037441, "getrandom", false, 4165697604335203851]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ring-67359dc48f9ea57f\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}