{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 17212183120498275042, "deps": [[1988483478007900009, "unicode_ident", false, 18424735439442073346], [3060637413840920116, "proc_macro2", false, 11922922787390032898], [17990358020177143287, "quote", false, 2646576083173682583]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\syn-76d384b491698ec3\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}