{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 5257651611800219828, "deps": [[8413798824750015470, "cc", false, 3152528798611396653]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ring-c90f1bd29a492b26\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}