{"rustc": 1842507548689473721, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 5627820096486484124, "path": 14936214404469579842, "deps": [[7911289239703230891, "adler2", false, 7511190275011328210]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-e0a35cca15a8c0c5\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}