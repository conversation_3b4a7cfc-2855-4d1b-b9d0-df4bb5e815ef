{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 14140399074051949153, "profile": 8731458305071235362, "path": 10165398118557108456, "deps": [[722509084658410873, "ingestion_service", false, 16555131514952900281], [1441306149310335789, "tempfile", false, 8249039906580876076], [1760623714118191065, "dotenv", false, 15522334084148677918], [3445762082367621908, "qdrant_client", false, 2983131463448444523], [3601586811267292532, "tower", false, 17828702658272868184], [4891297352905791595, "axum", false, 17622659750116656944], [5061771074803198238, "mail_parser", false, 6857506136370527911], [7244058819997729774, "reqwest", false, 13684463436975701535], [8319709847752024821, "uuid", false, 15569704690176671513], [8606274917505247608, "tracing", false, 18414157313688031362], [9689903380558560274, "serde", false, 16584340690927804728], [9857275760291862238, "sha2", false, 3751157022612340912], [9897246384292347999, "chrono", false, 9740778159667974192], [12393800526703971956, "tokio", false, 1898190954426490280], [14435908599267459652, "tower_http", false, 1280183306737993007], [15367738274754116744, "serde_json", false, 12129642665611885088], [16230660778393187092, "tracing_subscriber", false, 4090747980315965993], [17565085470850576164, "html2text", false, 10031076678648376757]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ingestion_service-292625425d8f4f0e\\dep-example-test_processor", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}