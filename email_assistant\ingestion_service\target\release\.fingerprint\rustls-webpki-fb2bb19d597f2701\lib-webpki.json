{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 2040997289075261528, "path": 12590007092919164603, "deps": [[2883436298747778685, "pki_types", false, 18021711389995360], [5491919304041016563, "ring", false, 9036138397631959715], [8995469080876806959, "untrusted", false, 9522255962815586883]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-webpki-fb2bb19d597f2701\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}